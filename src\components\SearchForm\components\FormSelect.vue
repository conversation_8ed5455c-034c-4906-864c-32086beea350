<template>
  <a-select
    :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
    v-model:value="item.value"
    :placeholder="item.label"
    :filter-option="filterOption"
    :disabled="(item as any).disabled"
    :mode="(item as any).multiple ? 'multiple' : null"
    :maxTagCount="(item as any).multiple ? 'responsive' : null"
    allowClear
    showArrow
    class="w-140px"
    :options="(item as any).options"
  >
    <!-- 显式渲染选项，确保正确显示标签 -->
    <a-select-option v-for="option in (item as any).options" :key="option.value" :value="option.value" :label="option.label">
      {{ option.label }}
    </a-select-option>
  </a-select>
</template>

<script setup lang="ts">
import { filterOption } from '@/utils/index'
import { FormItemType } from '../type'

defineEmits<{
  (e: 'serach'): void
}>()

const item = defineModel<FormItemType<'select'>>('item', { required: true })
</script>

<style scoped></style>
